// 测试模块加载问题修复
// 验证TodoTool和MemoryTool是否能正常加载

console.log('=== 模块加载测试 ===')

try {
  console.log('1. 测试MemoryTool加载...')
  const MemoryTool = require('./modules/memory')
  const memoryTool = new MemoryTool()
  console.log('✅ MemoryTool加载成功')
  
  console.log('2. 测试TodoTool加载...')
  const TodoTool = require('./modules/todo')
  const todoTool = new TodoTool()
  console.log('✅ TodoTool加载成功')
  
  console.log('3. 测试系统提示词构建...')
  const { buildSystemPrompt } = require('./modules/prompts/prompt')
  const mockTimeInfo = {
    current_datetime: '2024-01-15 14:30:00',
    current_weekday: '星期一',
    current_date: '2024-01-15'
  }
  const mockDates = {
    tomorrow: '2024-01-16',
    yesterday: '2024-01-14'
  }
  
  const systemPrompt = buildSystemPrompt({
    timeInfo: mockTimeInfo,
    dates: mockDates
  })
  console.log('✅ 系统提示词构建成功')
  
  console.log('4. 测试包含记忆的系统提示词构建...')
  const mockMemories = [
    { content: '我是前端开发者' },
    { content: '我喜欢早上工作' }
  ]
  
  const systemPromptWithMemory = buildSystemPrompt({
    timeInfo: mockTimeInfo,
    dates: mockDates
  }, {
    memories: mockMemories
  })
  
  const hasMemorySection = systemPromptWithMemory.includes('【用户记忆】')
  if (hasMemorySection) {
    console.log('✅ 包含记忆的系统提示词构建成功')
  } else {
    console.log('❌ 记忆内容未正确融入系统提示词')
  }
  
  console.log('\n=== 所有模块加载测试通过 ===')
  
} catch (error) {
  console.error('❌ 模块加载失败:', error.message)
  console.error('错误堆栈:', error.stack)
}
