<template>
  <view v-if="visible" class="task-detail-modal" @click="handleMaskClick">
    <view class="modal-content" @click.stop>
      <!-- 模态框头部 -->
      <view class="modal-header">
        <view class="header-title-wrapper">
          <i :class="getOperationIcon(operationType)"></i>
          <text class="header-title">{{ getOperationTitle(operationType) }}</text>
        </view>
        <view class="close-btn" @click="handleClose">
          <i class="fas fa-times"></i>
        </view>
      </view>

      <!-- 任务详情内容 -->
      <view class="modal-body">
        <!-- 任务标题 -->
        <view class="detail-section">
          <view class="section-header">
            <i class="fas fa-heading"></i>
            <text class="section-title">任务标题</text>
          </view>
          <view class="section-content">
            <text class="task-title">{{ taskData.title || '未命名任务' }}</text>
          </view>
        </view>

        <!-- 任务描述 -->
        <view v-if="taskData.content || taskData.description" class="detail-section">
          <view class="section-header">
            <i class="fas fa-align-left"></i>
            <text class="section-title">任务描述</text>
          </view>
          <view class="section-content">
            <text class="task-description">{{ taskData.content || taskData.description }}</text>
          </view>
        </view>

        <!-- 基本信息网格 -->
        <view class="info-grid">
          <!-- 项目信息 -->
          <view v-if="taskData.projectName" class="info-item">
            <view class="info-label">
              <i class="fas fa-folder"></i>
              <text>项目</text>
            </view>
            <view class="info-value">{{ taskData.projectName }}</view>
          </view>

          <!-- 优先级 -->
          <view v-if="taskData.priority > 0" class="info-item">
            <view class="info-label">
              <i class="fas fa-flag"></i>
              <text>优先级</text>
            </view>
            <view class="info-value priority-value" :class="getPriorityClass(taskData.priority)">
              {{ getPriorityLabel(taskData.priority) }}
            </view>
          </view>

          <!-- 任务类型 -->
          <view v-if="taskData.type" class="info-item">
            <view class="info-label">
              <i class="fas fa-tag"></i>
              <text>类型</text>
            </view>
            <view class="info-value">{{ getTypeLabel(taskData.type) }}</view>
          </view>

          <!-- 权重 -->
          <view v-if="taskData.weight !== undefined && taskData.weight !== null && taskData.weight !== 0" class="info-item">
            <view class="info-label">
              <i class="fas fa-weight-hanging"></i>
              <text>权重</text>
            </view>
            <view class="info-value">{{ taskData.weight }}</view>
          </view>

          <!-- 提醒设置 -->
          <view v-if="taskData.reminder" class="info-item">
            <view class="info-label">
              <i class="fas fa-bell"></i>
              <text>提醒</text>
            </view>
            <view class="info-value">{{ getReminderLabel(taskData.reminder) }}</view>
          </view>

          <!-- 全天任务 -->
          <view v-if="taskData.isAllDay" class="info-item">
            <view class="info-label">
              <i class="fas fa-clock"></i>
              <text>时间</text>
            </view>
            <view class="info-value">全天任务</view>
          </view>
        </view>

        <!-- 标签 -->
        <view v-if="taskData.tagNames && taskData.tagNames.length" class="detail-section">
          <view class="section-header">
            <i class="fas fa-tags"></i>
            <text class="section-title">标签</text>
          </view>
          <view class="section-content">
            <view class="task-tags">
              <view v-for="tag in taskData.tagNames" :key="tag" class="tag">{{ tag }}</view>
            </view>
          </view>
        </view>

        <!-- 时间信息 -->
        <view v-if="taskData.startDate || taskData.dueDate" class="detail-section">
          <view class="section-header">
            <i class="fas fa-calendar-alt"></i>
            <text class="section-title">时间安排</text>
          </view>
          <view class="section-content">
            <view class="time-info">
              <view v-if="taskData.startDate" class="time-item">
                <i class="fas fa-play-circle"></i>
                <text class="time-label">开始时间：</text>
                <text class="time-value">{{ formatTaskDate(taskData.startDate, taskData.isAllDay) }}</text>
              </view>
              <view v-if="taskData.dueDate" class="time-item">
                <i class="fas fa-stop-circle"></i>
                <text class="time-label">截止时间：</text>
                <text class="time-value">{{ formatTaskDate(taskData.dueDate, taskData.isAllDay) }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 变更信息（仅更新操作显示） -->
        <view v-if="operationType === 'update' && taskData.changedFields && taskData.changedFields.length" class="detail-section">
          <view class="section-header">
            <i class="fas fa-edit"></i>
            <text class="section-title">变更信息</text>
          </view>
          <view class="section-content">
            <view class="changes-summary">
              <text class="changes-label">变更字段：</text>
              <text class="changes-fields">{{ getFieldLabels(taskData.changedFields).join(', ') }}</text>
            </view>
            <view v-if="taskData.diff" class="diff-list">
              <view v-for="(diffValue, field) in taskData.diff" :key="field" class="diff-item">
                <text class="field-name">{{ getFieldLabel(field) }}</text>
                <i class="fas fa-arrow-right diff-arrow"></i>
                <text class="field-value">{{ formatDiffValue(diffValue.after) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 模态框底部 -->
      <view class="modal-footer">
        <view class="footer-btn close-btn-footer" @click="handleClose">
          <i class="fas fa-times"></i>
          <text>关闭</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  taskData: {
    type: Object,
    default: () => ({}),
  },
  operationType: {
    type: String,
    default: 'create', // 'create', 'update', 'delete'
  },
})

const emit = defineEmits(['close'])

// 处理遮罩点击
const handleMaskClick = () => {
  handleClose()
}

// 处理关闭
const handleClose = () => {
  emit('close')
}

// 获取操作图标
const getOperationIcon = (type) => {
  const iconMap = {
    create: 'fas fa-plus-circle',
    update: 'fas fa-edit',
    delete: 'fas fa-trash-alt',
  }
  return iconMap[type] || 'fas fa-info-circle'
}

// 获取操作标题
const getOperationTitle = (type) => {
  const titleMap = {
    create: '任务详情 - 新增',
    update: '任务详情 - 更新',
    delete: '任务详情 - 删除',
  }
  return titleMap[type] || '任务详情'
}

// 优先级标签映射
const getPriorityLabel = (priority) => {
  const priorityMap = {
    1: '低',
    2: '中低',
    3: '中',
    4: '中高',
    5: '高',
  }
  return priorityMap[priority] || `优先级 ${priority}`
}

// 获取优先级样式类
const getPriorityClass = (priority) => {
  if (priority >= 4) return 'priority-high'
  if (priority >= 3) return 'priority-medium'
  return 'priority-low'
}

// 任务类型标签映射
const getTypeLabel = (type) => {
  const typeMap = {
    kr: '关键结果',
    todo: '待办任务',
    habit: '习惯',
    TEXT: '文本任务',
    CHECKLIST: '清单任务',
    NOTE: '笔记任务',
  }
  return typeMap[type] || type
}

// 提醒标签映射
const getReminderLabel = (reminder) => {
  if (!reminder) return ''
  if (reminder === '0') return '准时提醒'
  if (reminder.includes('M')) return `提前${reminder.replace('-', '').replace('M', '分钟')}`
  if (reminder.includes('H')) return `提前${reminder.replace('-', '').replace('H', '小时')}`
  if (reminder.includes('D')) return `提前${reminder.replace('-', '').replace('D', '天')}`
  return reminder
}

// 字段名称映射
const getFieldLabel = (field) => {
  const fieldMap = {
    title: '标题',
    content: '内容',
    description: '描述',
    startDate: '开始时间',
    dueDate: '截止时间',
    priority: '优先级',
    status: '状态',
    projectName: '项目',
    tagNames: '标签',
    weight: '权重',
    type: '类型',
    kind: '种类',
    projectId: '项目ID',
    isAllDay: '全天',
    reminder: '提醒',
  }
  return fieldMap[field] || field
}

// 批量获取字段标签
const getFieldLabels = (fields) => {
  return fields.map((field) => getFieldLabel(field))
}

// 格式化差异值
const formatDiffValue = (value) => {
  if (value === null || value === undefined) return ''
  if (typeof value === 'string') return value
  try {
    return JSON.stringify(value)
  } catch {
    return String(value)
  }
}

// 格式化任务日期
const formatTaskDate = (dateStr, isAllDay = false) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    if (isAllDay) {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      })
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
    }
  } catch {
    return dateStr
  }
}
</script>

<style lang="scss" scoped>
.task-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .modal-content {
    background-color: var(--color-white);
    border-radius: var(--rounded-lg);
    box-shadow: var(--shadow-lg);
    max-width: 90%;
    max-height: 80%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--color-gray-200);
    background-color: var(--color-bg-card);

    .header-title-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 18px;
        color: var(--color-primary);
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--color-text-primary);
      }
    }

    .close-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: var(--color-gray-100);
      cursor: pointer;
      transition: var(--transition-fast);

      &:hover {
        background-color: var(--color-gray-200);
      }

      i {
        font-size: 14px;
        color: var(--color-gray-600);
      }
    }
  }

  .modal-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .detail-section {
    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      i {
        font-size: 14px;
        color: var(--color-primary);
      }

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--color-text-primary);
      }
    }

    .section-content {
      padding-left: 22px;

      .task-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--color-text-primary);
        line-height: 1.4;
      }

      .task-description {
        font-size: 14px;
        color: var(--color-text-secondary);
        line-height: 1.5;
        white-space: pre-wrap;
      }
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 12px;
      background-color: var(--color-gray-100);
      border-radius: var(--rounded-sm);

      .info-label {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: var(--color-text-secondary);

        i {
          font-size: 11px;
        }
      }

      .info-value {
        font-size: 14px;
        color: var(--color-text-primary);
        font-weight: 500;

        &.priority-value {
          &.priority-high {
            color: var(--color-danger);
          }

          &.priority-medium {
            color: var(--color-warning);
          }

          &.priority-low {
            color: var(--color-success);
          }
        }
      }
    }
  }

  .task-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .tag {
      font-size: 12px;
      padding: 4px 8px;
      background-color: var(--color-primary);
      color: white;
      border-radius: var(--rounded-sm);
      line-height: 1.2;
    }
  }

  .time-info {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .time-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;

      i {
        font-size: 12px;
        color: var(--color-primary);
        width: 16px;
      }

      .time-label {
        color: var(--color-text-secondary);
        min-width: 80px;
      }

      .time-value {
        color: var(--color-text-primary);
        font-weight: 500;
      }
    }
  }

  .changes-summary {
    margin-bottom: 12px;
    font-size: 14px;

    .changes-label {
      color: var(--color-text-secondary);
      font-weight: 500;
    }

    .changes-fields {
      color: var(--color-text-primary);
    }
  }

  .diff-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .diff-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: var(--color-gray-100);
      border-radius: var(--rounded-sm);
      font-size: 13px;

      .field-name {
        color: var(--color-text-secondary);
        font-weight: 500;
        min-width: 60px;
      }

      .diff-arrow {
        color: var(--color-primary);
        font-size: 11px;
      }

      .field-value {
        color: var(--color-text-primary);
        word-break: break-all;
        flex: 1;
      }
    }
  }

  .modal-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--color-gray-200);
    background-color: var(--color-bg-card);
    display: flex;
    justify-content: center;

    .footer-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 10px 20px;
      border-radius: var(--rounded-sm);
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition-fast);

      &.close-btn-footer {
        background-color: var(--color-gray-200);
        color: var(--color-text-secondary);

        &:hover {
          background-color: var(--color-gray-300);
        }

        i {
          font-size: 14px;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-detail-modal {
    padding: 10px;

    .modal-content {
      max-width: 100%;
      max-height: 90%;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
