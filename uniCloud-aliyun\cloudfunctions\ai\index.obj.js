const { OpenAI } = require('openai')
const {
  FUNCTION_TOOLS,
  doubaoParams,
  SSE_MESSAGE_TYPES,
  createSSEMessage,
  generateSessionId,
} = require('./modules/config.js')
const TodoTool = require('./modules/todo')
const MemoryTool = require('./modules/memory')
const db = uniCloud.database()

// 工具执行详情构建配置与辅助函数（SSE 详情增强）
const DETAILS_LIMITS = {
  MAX_LIST: 20,
  STRING_MAX_LEN: 120,
}

function inferOperationTypeFromTool(toolName, result) {
  if (result && typeof result.operationType === 'string') {
    const t = String(result.operationType).toLowerCase()
    if (t === 'create' || t === 'update' || t === 'delete' || t === 'query') return t
  }
  const name = (toolName || '').toLowerCase()
  if (name.startsWith('get')) return 'query'
  if (name.startsWith('create')) return 'create'
  if (name.startsWith('update') || name.includes('rename') || name.includes('merge')) return 'update'
  if (name.startsWith('delete')) return 'delete'
  // 兜底：无匹配视为查询
  return 'query'
}

function truncateString(s) {
  if (typeof s !== 'string') return s
  return s.length > DETAILS_LIMITS.STRING_MAX_LEN ? s.slice(0, DETAILS_LIMITS.STRING_MAX_LEN) + '…' : s
}

function ensureArray(v) {
  if (Array.isArray(v)) return v
  if (v === null || v === undefined) return []
  return [v]
}

function pickId(obj) {
  if (!obj || typeof obj !== 'object') return null
  return obj.id || obj._id || obj.taskId || obj.projectId || null
}

function buildDetails(operationType, toolName, result, parameters) {
  try {
    // 仅对非查询类返回详情
    if (!operationType || operationType === 'query') return null

    const data = result && result.data
    const items = ensureArray(data)

    let truncated = false
    let list = items
    if (list.length > DETAILS_LIMITS.MAX_LIST) {
      truncated = true
      list = list.slice(0, DETAILS_LIMITS.MAX_LIST)
    }

    const ids = []
    for (const it of list) {
      const id = pickId(it)
      if (id) ids.push(String(id))
      if (ids.length >= DETAILS_LIMITS.MAX_LIST) break
    }

    const base = {
      operationType,
      summary: String(result?.message || ''),
      rowsAffected: 0,
      affectedIds: ids,
      truncated,
    }

    if (operationType === 'create') {
      const createdIds = ids.length > 0 ? ids : parameters && parameters.id ? [String(parameters.id)] : []
      const preview = list
        .map((it) => {
          // 构建完整的任务信息对象，只包含有值的字段
          const taskInfo = {}

          // 基础字段
          if (pickId(it)) taskInfo._id = pickId(it)
          if (it && (it.title || it.name || it.content)) taskInfo.title = it.title || it.name || it.content

          // 时间字段
          if (it && it.startDate) taskInfo.startDate = it.startDate
          if (it && it.dueDate) taskInfo.dueDate = it.dueDate

          // 扩展字段 - 只添加有值的字段
          if (it && it.content && it.content !== taskInfo.title) taskInfo.content = it.content
          if (it && it.priority !== undefined && it.priority !== null) taskInfo.priority = it.priority
          if (it && it.projectName) taskInfo.projectName = it.projectName
          if (it && it.tagNames && Array.isArray(it.tagNames) && it.tagNames.length > 0) taskInfo.tagNames = it.tagNames
          if (it && it.weight !== undefined && it.weight !== null && it.weight !== 0) taskInfo.weight = it.weight
          if (it && it.type) taskInfo.type = it.type
          if (it && it.status !== undefined && it.status !== null) taskInfo.status = it.status
          if (it && it.kind) taskInfo.kind = it.kind
          if (it && it.description && it.description !== taskInfo.title) taskInfo.description = it.description

          // 滴答清单特有字段
          if (it && it.projectId) taskInfo.projectId = it.projectId
          if (it && it.isAllDay !== undefined) taskInfo.isAllDay = it.isAllDay
          if (it && it.reminder) taskInfo.reminder = it.reminder

          return Object.keys(taskInfo).length > 0 ? taskInfo : null
        })
        .filter(Boolean)
        .slice(0, DETAILS_LIMITS.MAX_LIST)
      return {
        ...base,
        createdIds,
        createdCount: createdIds.length || (items.length > 0 ? items.length : 0),
        rowsAffected: createdIds.length || (items.length > 0 ? items.length : 0),
        preview,
      }
    }

    if (operationType === 'update') {
      // 尝试解析更新对象 ID
      const primaryId =
        ids[0] || (parameters && (parameters.taskId || parameters.projectIdOrName || parameters.tagIdOrName)) || null
      const changedFields = []
      const diff = {}
      if (parameters && typeof parameters === 'object') {
        for (const [k, v] of Object.entries(parameters)) {
          // 跳过定位字段与空值
          if (k === 'taskId' || k === 'projectIdOrName' || k === 'tagIdOrName') continue
          if (v === undefined) continue
          changedFields.push(k)
          diff[k] = { before: null, after: truncateString(typeof v === 'string' ? v : JSON.stringify(v)) }
        }
      }
      const changeEntry = {
        _id: primaryId ? String(primaryId) : undefined,
        changedFields,
        diff,
        title: (list[0] && (list[0].title || list[0].name)) || undefined,
      }
      const updatedIds = primaryId ? [String(primaryId)] : ids
      return {
        ...base,
        updatedIds,
        updatedCount: updatedIds.length || 1,
        rowsAffected: updatedIds.length || 1,
        changes: [changeEntry].slice(0, DETAILS_LIMITS.MAX_LIST),
      }
    }

    if (operationType === 'delete') {
      const deletedIds =
        ids.length > 0
          ? ids
          : parameters && (parameters.taskId || parameters.projectIdOrName || parameters.tagIdOrName)
          ? [String(parameters.taskId || parameters.projectIdOrName || parameters.tagIdOrName)]
          : []
      const titles = list
        .map((it) => (it && (it.title || it.name)) || null)
        .filter(Boolean)
        .slice(0, DETAILS_LIMITS.MAX_LIST)
      return {
        ...base,
        deletedIds,
        deletedCount: deletedIds.length || 1,
        rowsAffected: deletedIds.length || 1,
        titles,
      }
    }

    // 未知类型兜底：不返回详情
    return null
  } catch (e) {
    console.warn('[buildDetails] 构建详情失败，忽略', { error: e && e.message })
    return null
  }
}

/**
 * 会话持久化：运行状态 & 取消标记
 */
async function upsertSession(sessionId, data) {
  const now = new Date().toISOString()
  const payload = { updateTime: now, ...(data || {}) }
  if (data && data.status === 'running') {
    payload.createTime = now
    payload.canceled = false
  }
  try {
    const res = await db.collection('aiSession').doc(sessionId).set(payload)
  } catch (e) {
    console.error('[aiSession] upsertSession.set error', { sessionId, error: e && e.message })
    throw e
  }
}

async function markSession(sessionId, updates) {
  const now = new Date().toISOString()
  try {
    const updatePayload = { ...(updates || {}), updateTime: now }
    const res = await db.collection('aiSession').doc(sessionId).update(updatePayload)
    return res
  } catch (e) {
    console.error('[aiSession] markSession.update error', { sessionId, error: e && e.message })
    throw e
  }
}

/**
 * 流式工具调用处理（豆包模型优化版）
 */
async function handleStreamResponse(streamResponse, sseChannel, sessionId, originalMessages, options = {}) {
  let pendingToolCalls = [] // 使用数组存储工具调用，支持索引
  let assistantMessage = ''
  let hasToolCalls = false
  let firstChunkLogged = false

  for await (const chunk of streamResponse) {
    // 协作式取消：仅读取内存标志，避免每个 token 查库
    if (options && typeof options.isCanceled === 'function' && options.isCanceled()) {
      try {
        if (options && typeof options.onCanceled === 'function') {
          options.onCanceled()
        }
      } catch (_) {}
      await sseChannel.end(
        createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
          message: '用户已取消',
          reason: 'canceled',
        })
      )
      return
    }
    if (!firstChunkLogged) {
      firstChunkLogged = true
    }
    const delta = chunk.choices[0]?.delta
    const finishReason = chunk.choices[0]?.finish_reason

    // 处理普通文本内容
    if (delta?.content) {
      assistantMessage += delta.content
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: delta.content,
          isComplete: false,
        })
      )
    }

    // 处理工具调用 - 豆包模型增量式处理
    if (delta?.tool_calls) {
      hasToolCalls = true

      for (const toolCallDelta of delta.tool_calls) {
        const index = toolCallDelta.index || 0
        const toolCallId = toolCallDelta.id
        const name = toolCallDelta.function?.name
        const argsChunk = toolCallDelta.function?.arguments

        // 初始化工具调用对象
        if (!pendingToolCalls[index]) {
          pendingToolCalls[index] = {
            id: toolCallId || `call_${Date.now()}_${index}`,
            type: 'function',
            function: {
              name: toolCallDelta.function?.name || '',
              arguments: toolCallDelta.function?.arguments || '',
            },
          }

          // 推送工具调用开始消息
          if (toolCallDelta.function?.name) {
            await sseChannel.write(
              createSSEMessage(SSE_MESSAGE_TYPES.TOOL_CALL_START, sessionId, {
                toolName: toolCallDelta.function.name,
                toolCallId: pendingToolCalls[index].id,
              })
            )
          }
        } else {
          // 累积工具调用参数
          if (toolCallDelta.function?.name) {
            pendingToolCalls[index].function.name = toolCallDelta.function.name
          }
          if (toolCallDelta.function?.arguments) {
            pendingToolCalls[index].function.arguments += toolCallDelta.function.arguments
          }
        }
      }
    }

    // 检查是否完成工具调用
    if (finishReason === 'tool_calls' && hasToolCalls) {
      // 执行所有完整的工具调用
      const toolResults = []

      const completeToolCalls = pendingToolCalls.filter((tc) => tc && tc.function.name)

      for (const toolCall of completeToolCalls) {
        try {
          const result = await executeToolCall(toolCall, sseChannel, sessionId)
          toolResults.push({
            toolCall: toolCall,
            result: result,
          })
        } catch (error) {
          console.error('工具调用执行失败：', error.message)
          await sseChannel.write(
            createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
              toolName: toolCall.function.name,
              error: error.message,
            })
          )

          // 即使失败也要记录，以便后续处理
          toolResults.push({
            toolCall: toolCall,
            result: { error: error.message, success: false },
          })
        }
      }

      // 继续对话，让模型基于工具结果生成最终回复
      if (toolResults.length > 0) {
        await continueConversationWithToolResults(
          originalMessages,
          pendingToolCalls.filter((tc) => tc),
          toolResults,
          sseChannel,
          sessionId,
          options
        )
      }
    }

    // 处理普通对话结束
    if (finishReason === 'stop' && !hasToolCalls) {
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: '',
          isComplete: true,
        })
      )
    }
  }
}

/**
 * 继续对话 - 将工具调用结果传回模型
 */
async function continueConversationWithToolResults(
  originalMessages,
  toolCalls,
  toolResults,
  sseChannel,
  sessionId,
  options = {}
) {
  const openai = new OpenAI(doubaoParams)

  // 构建包含工具调用和结果的完整消息历史
  const messagesWithToolResults = [
    ...originalMessages,
    // 添加助手的工具调用消息
    {
      role: 'assistant',
      content: null,
      tool_calls: toolCalls,
    },
    // 添加工具执行结果消息
    ...toolResults.map(({ toolCall, result }) => ({
      role: 'tool',
      tool_call_id: toolCall.id,
      content: JSON.stringify(result),
    })),
  ]

  // 推送工具结果处理开始消息
  await sseChannel.write(
    createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_PROCESSING, sessionId, {
      message: '正在基于工具执行结果生成回复...',
    })
  )

  try {
    // 轮次与时间控制
    const maxRounds = options?.maxRounds ?? 5
    const startedAt = options?.startedAt ?? Date.now()
    const nextOptions = {
      ...options,
      round: (options?.round ?? 0) + 1,
      maxRounds,
      startedAt,
    }

    // 超过最大轮次则停止
    if (nextOptions.round > maxRounds) {
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: `已达到最大工具调用轮次（${maxRounds}），如需继续请确认或再发指令。`,
          isComplete: true,
        })
      )
      return
    }

    // 发起后续对话（允许再次工具调用）
    const followUpResponse = await openai.chat.completions.create({
      model: 'doubao-seed-1-6-flash-250715',
      messages: messagesWithToolResults,
      tools: FUNCTION_TOOLS,
      tool_choice: 'auto',
      stream: true,
      timeout: 60000,
      thinking: { type: 'disabled' },
      // 传入取消信号（如有）
      ...(options && options.abortController ? { signal: options.abortController.signal } : {}),
    })

    // 将后续流交回统一处理（支持多轮）
    return await handleStreamResponse(followUpResponse, sseChannel, sessionId, messagesWithToolResults, {
      ...nextOptions,
      onCanceled: options && options.onCanceled,
      abortController: options && options.abortController,
      isCanceled: options && options.isCanceled,
    })
  } catch (error) {
    console.error('工具结果处理失败：', error)
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_ERROR, sessionId, {
        error: '基于工具结果生成回复失败',
        details: error.message,
      })
    )
  }
}

/**
 * 统一工具执行接口
 */
async function executeToolCall(toolCall, sseChannel, sessionId) {
  const { function: func } = toolCall
  const toolName = func.name

  try {
    const parameters = JSON.parse(func.arguments)

    // 推送工具执行开始消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_START, sessionId, {
        toolName: toolName,
        parameters: parameters,
      })
    )

    let result

    // 根据工具名称路由到不同的工具
    const memoryToolMethods = ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory']

    if (memoryToolMethods.includes(toolName)) {
      // 记忆工具调用
      const memoryTool = new MemoryTool()
      // 获取用户ID - 从云对象上下文中获取
      // 在实际部署时，应该通过uni-id获取当前登录用户的uid
      // 这里先使用一个固定的测试用户ID，后续需要集成uni-id认证
      const userId = 'test_user_001' // 临时测试用户ID
      const parametersWithUserId = { ...parameters, userId }
      result = await memoryTool.execute(toolName, parametersWithUserId)
    } else {
      // 其他工具通过 TodoTool 路由
      const todoTool = new TodoTool()
      result = await todoTool.execute(toolName, parameters)
    }

    // 统一错误语义：工具层如返回 errCode，视为失败并抛错
    if (result && result.errCode !== undefined && result.errCode !== null && result.errCode !== 0) {
      throw new Error(result.errMsg || '工具执行失败')
    }

    // 打印工具原始返回值，便于排查/观察
    try {
      console.log('[executeToolCall] tool raw result:', JSON.stringify(result, null, 2))
    } catch (_) {
      console.log('[executeToolCall] tool raw result (non-serializable)')
    }

    // 推送工具执行完成消息（仅发送精简摘要，避免 SSE 过大）
    const dataCount = Array.isArray(result?.data)
      ? result.data.length
      : result && result.data && typeof result.data.length === 'number'
      ? result.data.length
      : Array.isArray(result?.items)
      ? result.items.length
      : Array.isArray(result?.tasks)
      ? result.tasks.length
      : 0

    const baseMessage = result && result.message ? String(result.message) : ''
    const summarizedMessage =
      dataCount > 0 ? `${baseMessage ? baseMessage + '；' : ''}共 ${dataCount} 条数据` : baseMessage

    // 构建详情（仅非查询类工具）
    const operationType = inferOperationTypeFromTool(toolName, result)
    const details = buildDetails(operationType, toolName, result, parameters)

    const summarizedResult = {
      message: summarizedMessage,
      executionTime: result?.executionTime || null,
      dataCount: dataCount,
      // 可根据需要保留轻量级状态码信息
      errCode: result && result.errCode !== undefined ? result.errCode : 0,
      ...(details ? { details } : {}),
    }

    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_COMPLETE, sessionId, {
        toolName: toolName,
        result: summarizedResult,
        success: true,
        toolCallId: toolCall.id,
      })
    )

    return result
  } catch (error) {
    console.error('[executeToolCall] error', { sessionId, toolName, toolCallId: toolCall?.id, error: error.message })
    // 推送工具执行失败消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
        toolName: toolName,
        error: error.message,
        success: false,
      })
    )

    throw error
  }
}

module.exports = {
  async chatStreamSSE({ channel, message, messages: history_records }) {
    const sessionId = generateSessionId()

    // 参数验证
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 推送开始处理消息
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.PROCESSING_START, sessionId, {
          message: '开始处理您的请求...',
        })
      )

      // 初始化会话记录
      await upsertSession(sessionId, { status: 'running' })

      // 初始化 AI 客户端
      const openai = new OpenAI(doubaoParams)

      // 🎯 获取当前真实时间信息（抽离为通用上下文）
      const { buildTimeContext } = require('./modules/time-context')
      const { timeInfo, raw } = buildTimeContext()
      const {
        tomorrow,
        yesterday,
        dayAfterTomorrow,
        dayBeforeYesterday,
        thisSaturday,
        thisSunday,
        nextMonday,
        lastDayOfMonth,
        firstDayOfNextMonth,
        lastDayOfNextMonth,
      } = raw

      // 🧠 自动加载用户记忆并构建增强系统提示词
      const { buildSystemPrompt } = require('./modules/prompts/prompt')
      let userMemories = null

      try {
        // 使用固定的测试用户ID（按照要求暂时跳过真实用户认证）
        const testUserId = 'test_user_001'

        // 导入记忆工具
        const MemoryTool = require('./modules/memory')
        const memoryTool = new MemoryTool()

        // 自动加载用户记忆
        const memoriesResult = await memoryTool.execute('getMemories', {
          userId: testUserId,
          limit: 10, // 加载最近10条记忆，避免提示词过长
        })

        // 检查记忆加载结果
        if (memoriesResult && memoriesResult.success && memoriesResult.data && memoriesResult.data.length > 0) {
          userMemories = memoriesResult.data
          console.log(`[chatStreamSSE] 已加载 ${userMemories.length} 条用户记忆`)
        } else {
          console.log('[chatStreamSSE] 未找到用户记忆或记忆为空')
        }
      } catch (memoryError) {
        console.error('[chatStreamSSE] 记忆加载失败，使用默认系统提示词:', memoryError.message)
        // 记忆功能异常时不影响正常对话，继续使用默认提示词
      }

      // 构建包含记忆的系统提示词
      const systemContent = buildSystemPrompt(
        {
          timeInfo,
          dates: {
            tomorrow: tomorrow.toLocaleDateString('zh-CN'),
            yesterday: yesterday.toLocaleDateString('zh-CN'),
            dayAfterTomorrow: dayAfterTomorrow.toLocaleDateString('zh-CN'),
            dayBeforeYesterday: dayBeforeYesterday.toLocaleDateString('zh-CN'),
            thisSaturday: thisSaturday.toLocaleDateString('zh-CN'),
            thisSunday: thisSunday.toLocaleDateString('zh-CN'),
            nextMonday: nextMonday.toLocaleDateString('zh-CN'),
            lastDayOfMonth: lastDayOfMonth.toLocaleDateString('zh-CN'),
            firstDayOfNextMonth: firstDayOfNextMonth.toLocaleDateString('zh-CN'),
            lastDayOfNextMonth: lastDayOfNextMonth.toLocaleDateString('zh-CN'),
          },
        },
        {
          memories: userMemories, // 传入用户记忆
        }
      )

      const messages = [
        {
          role: 'system',
          content: systemContent,
        },
        // 正确处理历史消息，保留工具调用相关信息
        ...history_records.map((msg) => ({
          role: msg.role,
          content: msg.content,
          // 保留工具调用相关信息
          ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
          ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
        })),
        {
          role: 'user',
          content: message,
        },
      ]

      // 创建流式响应
      const abortController = new AbortController()
      // 启动后台轮询：每 1s 查询一次取消标记，命中后立即中断并主动结束
      let canceledFlag = false
      let endSent = false
      const pollIntervalMs = 1000
      const pollTimer = setInterval(async () => {
        try {
          if (canceledFlag) return
          const { data } = await db.collection('aiSession').doc(sessionId).get()
          const doc = Array.isArray(data) ? data[0] : null
          if (doc && (doc.canceled === true || doc.status === 'canceled')) {
            canceledFlag = true
            clearInterval(pollTimer)
            // 命中取消：立即中止上游并尝试主动结束，避免等待下一个 chunk
            try {
              abortController.abort()
            } catch (_) {}
            if (!endSent) {
              try {
                await sseChannel.end(
                  createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
                    message: '用户已取消',
                    reason: 'canceled',
                  })
                )
                endSent = true
              } catch (_) {}
            }
            try {
              await markSession(sessionId, { status: 'canceled', canceled: true, canceledAt: new Date().toISOString() })
            } catch (_) {}
          }
        } catch (err) {
          console.error('[cancelPoll] 轮询异常', { sessionId, error: err && err.message, stack: err && err.stack })
        }
      }, pollIntervalMs)
      const streamResponse = await openai.chat.completions.create({
        model: 'doubao-seed-1-6-flash-250715',
        messages: messages,
        tools: FUNCTION_TOOLS,
        tool_choice: 'auto',
        stream: true,
        timeout: 60000,
        thinking: { type: 'disabled' },
        signal: abortController.signal,
      })

      // 处理流式响应 - 传入原始消息用于工具调用后续处理
      await handleStreamResponse(streamResponse, sseChannel, sessionId, messages, {
        round: 0,
        maxRounds: 5,
        startedAt: Date.now(),
        abortController,
        isCanceled: () => canceledFlag,
        onCanceled: () => {
          try {
            abortController.abort()
          } catch (_) {}
        },
      })

      // 推送会话结束消息（仅未取消且未主动发送过 end 时，发送 completed）
      if (!canceledFlag && !endSent) {
        try {
          await sseChannel.end(
            createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
              message: '处理完成',
              reason: 'completed',
            })
          )
        } catch (_) {}
        try {
          await markSession(sessionId, { status: 'completed' })
        } catch (_) {}
      } else {
      }
      try {
        clearInterval(pollTimer)
      } catch (_) {}

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'function_calling_complete',
          sessionId: sessionId,
        },
      }
    } catch (error) {
      console.error('chatStreamSSE 异常：', error.message, { sessionId })

      // 尝试推送错误消息
      try {
        const sseChannel = uniCloud.deserializeSSEChannel(channel)
        await sseChannel.end(
          createSSEMessage(SSE_MESSAGE_TYPES.ERROR, sessionId, {
            error: error.message,
            timestamp: new Date().toISOString(),
          })
        )
      } catch (channelError) {
        console.error('SSE 推送错误：', channelError)
      }

      await markSession(sessionId, { status: 'failed', error: error.message })
      try {
        clearInterval(pollTimer)
      } catch (_) {}

      return {
        errCode: 'SYSTEM_ERROR',
        errMsg: error.message || '系统处理失败',
        data: {
          type: 'system_error',
          sessionId: sessionId,
        },
      }
    }
  },
  /**
   * 取消当前会话
   */
  async cancelChat({ sessionId }) {
    if (!sessionId) {
      return { errCode: 'PARAM_IS_NULL', errMsg: 'sessionId 不能为空' }
    }
    try {
      await markSession(sessionId, { canceled: true, status: 'canceled', canceledAt: new Date().toISOString() })
      return { errCode: 0, errMsg: 'success' }
    } catch (e) {
      return { errCode: 'SYSTEM_ERROR', errMsg: e.message || '取消失败' }
    }
  },
  /**
   * 直接调用工具（不走对话流）
   * @param {string} toolName 工具方法名，如 'getTasks'|'createTask'|'getTags'|'createMemory'|'getMemories' 等
   * @param {object} parameters 工具参数对象
   * @param {string|null} token 可选，滴答清单 token；若未提供则尝试使用环境变量 DIDA_TOKEN
   * @param {string|null} userId 可选，用户ID，记忆工具需要此参数
   */
  async callToolDirect({ toolName, parameters = {}, token = null, userId = null }) {
    if (!toolName) {
      return { errCode: 'PARAM_IS_NULL', errMsg: 'toolName 不能为空' }
    }
    try {
      console.log('[callToolDirect] start', { toolName, parameters })

      // 根据工具名称路由到不同的工具
      const memoryToolMethods = ['createMemory', 'getMemories', 'updateMemory', 'deleteMemory']

      if (memoryToolMethods.includes(toolName)) {
        // 记忆工具调用
        const memoryTool = new MemoryTool()
        const effectiveUserId = userId || 'test_user_001' // 使用传入的userId或默认测试用户ID
        const parametersWithUserId = { ...parameters, userId: effectiveUserId }
        const res = await memoryTool.execute(toolName, parametersWithUserId)
        try {
          console.log('[callToolDirect] memory tool raw result:', JSON.stringify(res).slice(0, 1200))
        } catch (_) {}
        return res
      } else {
        // 其他工具通过 TodoTool 路由
        const todoTool = new TodoTool()
        const effectiveToken = token || process.env.DIDA_TOKEN || null
        if (effectiveToken) {
          const initRes = await todoTool.authManager.initWithToken(effectiveToken)
          console.log('[callToolDirect] initWithToken result', { errCode: initRes?.errCode, message: initRes?.errMsg })
          if (initRes && initRes.errCode) {
            return initRes
          }
        } else {
          console.warn('[callToolDirect] 未提供 token，将尝试直接执行（可能导致 401/500）')
        }
        const res = await todoTool.execute(toolName, parameters)
        try {
          console.log('[callToolDirect] tool raw result:', JSON.stringify(res).slice(0, 1200))
        } catch (_) {}
        return res
      }
    } catch (e) {
      console.error('[callToolDirect] error', e && e.message)
      return { errCode: 'SYSTEM_ERROR', errMsg: e?.message || 'callToolDirect 失败', data: null }
    }
  },
}
