<template>
  <view :class="!sys.isPC ? 'fixed-placeholder' : ''" class="page-margin">
    <!-- 顶部导航栏 -->
    <view :class="{ fixed: !sys.isPC }" class="w-full top-0 z-100">
      <view class="status_bar"></view>
      <view class="flex flex-1 h-80 justify-between items-center">
        <view class="flex items-center">
          <i class="fas fa-chevron-left text-30 mr-20" @click="goBack"></i>
          <view class="text-45">记忆管理</view>
        </view>
        <view class="flex items-center">
          <i class="fas fa-plus text-30 color-primary" @click="showAddModal = true"></i>
        </view>
      </view>
    </view>

    <!-- 记忆列表 -->
    <view class="memory-list">
      <view v-if="loading" class="loading-container">
        <view class="loading-text">加载中...</view>
      </view>

      <view v-else-if="memories.length === 0" class="empty-container">
        <i class="fas fa-brain empty-icon"></i>
        <view class="empty-text">暂无记忆</view>
        <view class="empty-tip">点击右上角 + 号添加记忆</view>
      </view>

      <view v-else>
        <view v-for="memory in memories" :key="memory._id" class="memory-item" @click="editMemory(memory)">
          <view class="memory-content">{{ memory.content }}</view>
          <view class="memory-meta">
            <view class="memory-time">{{ formatTime(memory.createTime) }}</view>
            <view class="memory-actions">
              <i class="fas fa-edit action-icon edit-icon" @click.stop="editMemory(memory)"></i>
              <i class="fas fa-trash action-icon delete-icon" @click.stop="confirmDelete(memory)"></i>
            </view>
          </view>
          <view v-if="memory.startDate || memory.endDate" class="memory-dates">
            <text v-if="memory.startDate">开始: {{ memory.startDate }}</text>
            <text v-if="memory.endDate">结束: {{ memory.endDate }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加记忆弹窗 -->
    <u-modal
      v-model="showAddModal"
      title="添加记忆"
      show-cancel-button
      mask-close-able
      confirm-text="保存"
      @confirm="saveMemory"
      @cancel="resetForm"
    >
      <view class="modal-content">
        <view class="form-item">
          <view class="form-label">记忆内容 *</view>
          <u-input
            v-model="formData.content"
            type="textarea"
            placeholder="请输入要记住的内容（最多2000字符）"
            :maxlength="2000"
            height="200"
          />
        </view>
        <view class="form-item">
          <view class="form-label">开始日期</view>
          <u-input v-model="formData.startDate" placeholder="YYYY-MM-DD（可选）" type="text" />
        </view>
        <view class="form-item">
          <view class="form-label">结束日期</view>
          <u-input v-model="formData.endDate" placeholder="YYYY-MM-DD（可选）" type="text" />
        </view>
      </view>
    </u-modal>

    <!-- 编辑记忆弹窗 -->
    <u-modal
      v-model="showEditModal"
      title="编辑记忆"
      show-cancel-button
      mask-close-able
      confirm-text="保存"
      @confirm="updateMemory"
      @cancel="resetForm"
    >
      <view class="modal-content">
        <view class="form-item">
          <view class="form-label">记忆内容 *</view>
          <u-input
            v-model="formData.content"
            type="textarea"
            placeholder="请输入要记住的内容（最多2000字符）"
            :maxlength="2000"
            height="200"
          />
        </view>
        <view class="form-item">
          <view class="form-label">开始日期</view>
          <u-input v-model="formData.startDate" placeholder="YYYY-MM-DD（可选）" type="text" />
        </view>
        <view class="form-item">
          <view class="form-label">结束日期</view>
          <u-input v-model="formData.endDate" placeholder="YYYY-MM-DD（可选）" type="text" />
        </view>
      </view>
    </u-modal>

    <!-- 删除确认弹窗 -->
    <u-modal
      v-model="showDeleteModal"
      title="删除记忆"
      content="确定要删除这条记忆吗？此操作不可恢复。"
      show-cancel-button
      mask-close-able
      confirm-text="删除"
      @confirm="deleteMemory"
    ></u-modal>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { router } from '@/utils/tools'
import useIsPC from '@/hooks/useIsPC'
import dayjs from 'dayjs'

const sys = useIsPC()

// 数据定义
const memories = ref([])
const loading = ref(false)
const showAddModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const currentMemory = ref(null)

// 表单数据
const formData = ref({
  content: '',
  startDate: '',
  endDate: '',
})

// 固定的用户ID（demo级别）
const USER_ID = 'test_user_001'

// 生命周期钩子
onMounted(() => {
  loadMemories()
})

// 加载记忆列表
const loadMemories = async () => {
  loading.value = true
  try {
    const result = await uniCloud.callFunction({
      name: 'ai',
      data: {
        toolName: 'getMemories',
        parameters: {},
        userId: USER_ID,
      },
    })

    if (result.result.errCode === 0) {
      memories.value = result.result.data || []
    } else {
      uni.showToast({
        title: result.result.errMsg || '加载失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('加载记忆失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 保存记忆
const saveMemory = async () => {
  if (!formData.value.content.trim()) {
    uni.showToast({
      title: '请输入记忆内容',
      icon: 'none',
    })
    return
  }

  try {
    const result = await uniCloud.callFunction({
      name: 'ai',
      data: {
        toolName: 'createMemory',
        parameters: {
          content: formData.value.content.trim(),
          startDate: formData.value.startDate || null,
          endDate: formData.value.endDate || null,
        },
        userId: USER_ID,
      },
    })

    if (result.result.errCode === 0) {
      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
      showAddModal.value = false
      resetForm()
      loadMemories()
    } else {
      uni.showToast({
        title: result.result.errMsg || '保存失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('保存记忆失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}

// 编辑记忆
const editMemory = (memory) => {
  currentMemory.value = memory
  formData.value = {
    content: memory.content,
    startDate: memory.startDate || '',
    endDate: memory.endDate || '',
  }
  showEditModal.value = true
}

// 更新记忆
const updateMemory = async () => {
  if (!formData.value.content.trim()) {
    uni.showToast({
      title: '请输入记忆内容',
      icon: 'none',
    })
    return
  }

  try {
    const result = await uniCloud.callFunction({
      name: 'ai',
      data: {
        toolName: 'updateMemory',
        parameters: {
          memoryId: currentMemory.value._id,
          content: formData.value.content.trim(),
          startDate: formData.value.startDate || null,
          endDate: formData.value.endDate || null,
        },
        userId: USER_ID,
      },
    })

    if (result.result.errCode === 0) {
      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
      showEditModal.value = false
      resetForm()
      loadMemories()
    } else {
      uni.showToast({
        title: result.result.errMsg || '更新失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('更新记忆失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none',
    })
  }
}

// 确认删除
const confirmDelete = (memory) => {
  currentMemory.value = memory
  showDeleteModal.value = true
}

// 删除记忆
const deleteMemory = async () => {
  try {
    const result = await uniCloud.callFunction({
      name: 'ai',
      data: {
        toolName: 'deleteMemory',
        parameters: {
          memoryId: currentMemory.value._id,
        },
        userId: USER_ID,
      },
    })

    if (result.result.errCode === 0) {
      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
      showDeleteModal.value = false
      loadMemories()
    } else {
      uni.showToast({
        title: result.result.errMsg || '删除失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('删除记忆失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none',
    })
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    content: '',
    startDate: '',
    endDate: '',
  }
  currentMemory.value = null
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm')
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.fixed-placeholder {
  padding-top: calc(var(--status-bar-height) + 80rpx);
}

.memory-list {
  padding: 30rpx 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  .loading-text {
    color: #999;
    font-size: 28rpx;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  .empty-icon {
    font-size: 80rpx;
    color: #ddd;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #999;
    margin-bottom: 10rpx;
  }

  .empty-tip {
    font-size: 24rpx;
    color: #ccc;
  }
}

.memory-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

  .memory-content {
    font-size: 30rpx;
    line-height: 1.6;
    color: #333;
    margin-bottom: 20rpx;
    word-break: break-all;
  }

  .memory-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .memory-time {
      font-size: 24rpx;
      color: #999;
    }

    .memory-actions {
      display: flex;
      gap: 20rpx;

      .action-icon {
        font-size: 28rpx;
        padding: 10rpx;

        &.edit-icon {
          color: var(--color-primary);
        }

        &.delete-icon {
          color: #ff4757;
        }
      }
    }
  }

  .memory-dates {
    margin-top: 15rpx;
    font-size: 24rpx;
    color: #666;

    text {
      margin-right: 20rpx;
    }
  }
}

.modal-content {
  padding: 30rpx;

  .form-item {
    margin-bottom: 30rpx;

    .form-label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 15rpx;
    }
  }
}
</style>
