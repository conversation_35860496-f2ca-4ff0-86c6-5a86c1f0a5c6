/**
 * Todo 清单管理模块
 * 负责清单的 CRUD 操作和查询功能
 */

const { API_CONFIG, ERROR_CODES } = require('./config.js')
const { createSuccessResponse, createErrorResponse, validateParams, removeEmptyFields } = require('./utils.js')

/**
 * 清单管理类
 */
class ProjectManager {
  constructor(authManager) {
    this.authManager = authManager
  }

  /**
   * 获取清单列表
   * @param {object} options - 查询参数（兼容保留，但为对齐 Python 版本，默认不做筛选）
   * @returns {object} 清单列表（对齐 Python 版字段：id, name, color, sortOrder, sortType, modifiedTime, description?, isArchived）
   */
  async getProjects(options = {}) {
    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[ProjectManager] [getProjects] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects } = batchResult.data
      const result = []

      for (const project of projects) {
        // 对齐 Python 版：不做 includeClosed/keyword 筛选，按字段映射输出
        const projected = removeEmptyFields({
          id: project.id,
          name: project.name,
          color: project.color,
          sortOrder: project.sortOrder,
          sortType: project.sortType,
          modifiedTime: project.modifiedTime,
          description: project.description,
          // Python 版包含 isArchived（若 API 不返回则默认 False）。
          // 这里优先使用后端字段，其次根据 closed 推断。
          isArchived: typeof project.isArchived === 'boolean' ? project.isArchived : !!project.closed,
        })

        result.push(projected)
      }

      return createSuccessResponse('获取清单列表成功', result)
    } catch (error) {
      console.error('[ProjectManager] [getProjects] 获取清单列表异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取清单列表失败', error)
    }
  }

  /**
   * 创建清单
   * @param {object} options - 清单数据
   * @returns {object} 创建结果
   */
  async createProject(options = {}) {
    // 与 Python 版本对齐：仅接收 name 与 color
    const { name = null, color = null } = options

    // 参数校验
    const validation = validateParams({ name }, ['name'])
    if (validation) {
      console.warn('[ProjectManager] [createProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 与 Python 版本对齐：不设置默认 color，不携带 kind/closed，增加 inAll: true
      const safeProjectData = {
        name: name,
        color: color,
        inAll: true,
      }

      // 移除空值字段
      const cleanProjectData = removeEmptyFields(safeProjectData)

      // 发送创建请求
      const result = await this.authManager._request('POST', API_CONFIG.PROJECT_URL, cleanProjectData)
      if (result.errCode) {
        console.error('[ProjectManager] [createProject] 创建清单失败：', result)
        return result
      }

      return createSuccessResponse('清单创建成功', result.data)
    } catch (error) {
      console.error('[ProjectManager] [createProject] 创建清单异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建清单失败', error)
    }
  }

  // 按 Python 版本对齐：不提供 getProject 方法

  /**
   * 更新清单（对齐 Python 版）
   * @param {object} options
   * @param {string} options.projectIdOrName - 清单 ID 或 清单名称（必填）
   * @param {string} [options.name] - 新名称（可选）
   * @param {string} [options.color] - 新颜色（可选）
   * @returns {object} 更新结果
   */
  async updateProject(options = {}) {
    const { projectIdOrName = null, name = undefined, color = undefined } = options

    // 参数校验
    const validation = validateParams({ projectIdOrName }, ['projectIdOrName'])
    if (validation) {
      console.warn('[ProjectManager] [updateProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 获取现有清单数据
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[ProjectManager] [updateProject] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects } = batchResult.data

      // 先按 ID，后按名称匹配
      let target = null
      for (const p of projects) {
        if (p.id === projectIdOrName) {
          target = p
          break
        }
      }
      if (!target) {
        for (const p of projects) {
          if (p.name === projectIdOrName) {
            target = p
            break
          }
        }
      }

      if (!target) {
        return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到ID或名称为 '${projectIdOrName}' 的清单`)
      }

      const projectId = target.id

      // 组装更新数据：未提供的字段使用原值；不包含 kind/closed
      const updatePayload = removeEmptyFields({
        id: projectId,
        name: name !== undefined ? name : target.name,
        color: color !== undefined ? color : target.color,
      })

      // 发送更新请求（对齐 Python：PUT）
      const result = await this.authManager._request('PUT', `${API_CONFIG.PROJECT_URL}/${projectId}`, updatePayload)
      if (result.errCode) {
        console.error('[ProjectManager] [updateProject] 更新清单失败：', result)
        return result
      }

      // 若无返回体，则以我们发送的数据作为结果并补齐 id
      const data =
        result && result.data && Object.keys(result.data).length > 0 ? result.data : { ...updatePayload, id: projectId }

      return createSuccessResponse('清单更新成功', data)
    } catch (error) {
      console.error('[ProjectManager] [updateProject] 更新清单异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新清单失败', error)
    }
  }

  /**
   * 删除清单（对齐 Python 版）
   * @param {object} options
   * @param {string} options.projectIdOrName - 清单 ID 或 清单名称（必填）
   * @returns {object} 删除结果
   */
  async deleteProject(options = {}) {
    const { projectIdOrName = null } = options

    // 参数校验
    const validation = validateParams({ projectIdOrName }, ['projectIdOrName'])
    if (validation) {
      console.warn('[ProjectManager] [deleteProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 获取现有清单数据以解析 ID 或 名称
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[ProjectManager] [deleteProject] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects } = batchResult.data
      let target = null
      for (const p of projects) {
        if (p.id === projectIdOrName) {
          target = p
          break
        }
      }
      if (!target) {
        for (const p of projects) {
          if (p.name === projectIdOrName) {
            target = p
            break
          }
        }
      }

      if (!target) {
        return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到ID或名称为 '${projectIdOrName}' 的清单`)
      }

      const projectId = target.id

      // 发送删除请求
      const result = await this.authManager._request('DELETE', `${API_CONFIG.PROJECT_URL}/${projectId}`)
      if (result.errCode) {
        console.error('[ProjectManager] [deleteProject] 删除清单失败：', result)
        return result
      }

      return createSuccessResponse('清单删除成功', target)
    } catch (error) {
      console.error('[ProjectManager] [deleteProject] 删除清单异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除清单失败', error)
    }
  }
}

module.exports = ProjectManager
