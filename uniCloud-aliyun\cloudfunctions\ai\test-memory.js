// 记忆功能测试脚本
// 用于验证记忆模块的基本功能

const MemoryTool = require('./modules/memory')

async function testMemoryFunctions() {
  console.log('=== 开始记忆功能测试 ===')
  
  const memoryTool = new MemoryTool()
  const testUserId = 'test_user_001'
  
  try {
    // 测试1: 创建记忆
    console.log('\n1. 测试创建记忆...')
    const createResult = await memoryTool.execute('createMemory', {
      userId: testUserId,
      content: '我喜欢在早上8点开始工作，这是我最有效率的时间段',
      startDate: '2024-01-01',
      endDate: '2024-12-31'
    })
    console.log('创建记忆结果:', JSON.stringify(createResult, null, 2))
    
    if (createResult.success) {
      const memoryId = createResult.data.memoryId
      
      // 测试2: 获取记忆列表
      console.log('\n2. 测试获取记忆列表...')
      const getResult = await memoryTool.execute('getMemories', {
        userId: testUserId,
        limit: 10
      })
      console.log('获取记忆列表结果:', JSON.stringify(getResult, null, 2))
      
      // 测试3: 更新记忆
      console.log('\n3. 测试更新记忆...')
      const updateResult = await memoryTool.execute('updateMemory', {
        userId: testUserId,
        memoryId: memoryId,
        content: '我喜欢在早上8点开始工作，这是我最有效率的时间段。我通常会先处理重要的任务。'
      })
      console.log('更新记忆结果:', JSON.stringify(updateResult, null, 2))
      
      // 测试4: 删除记忆
      console.log('\n4. 测试删除记忆...')
      const deleteResult = await memoryTool.execute('deleteMemory', {
        userId: testUserId,
        memoryId: memoryId
      })
      console.log('删除记忆结果:', JSON.stringify(deleteResult, null, 2))
    }
    
    // 测试5: 测试参数校验
    console.log('\n5. 测试参数校验...')
    const invalidResult = await memoryTool.execute('createMemory', {
      userId: testUserId
      // 缺少必需的 content 参数
    })
    console.log('参数校验测试结果:', JSON.stringify(invalidResult, null, 2))
    
    // 测试6: 测试未知方法
    console.log('\n6. 测试未知方法...')
    const unknownResult = await memoryTool.execute('unknownMethod', {
      userId: testUserId
    })
    console.log('未知方法测试结果:', JSON.stringify(unknownResult, null, 2))
    
  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
  
  console.log('\n=== 记忆功能测试完成 ===')
}

// 如果直接运行此脚本，执行测试
if (require.main === module) {
  testMemoryFunctions()
}

module.exports = { testMemoryFunctions }
