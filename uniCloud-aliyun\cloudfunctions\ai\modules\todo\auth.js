/**
 * Todo 认证管理模块
 * 负责用户登录、token 管理和认证状态维护
 */

const { API_CONFIG, ERROR_CODES } = require('./config.js')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  parseHttpResponse,
  extractTokenFromCookies,
} = require('./utils.js')

/**
 * 认证管理类
 */
class AuthManager {
  constructor() {
    // 认证状态
    this.token = null
    this.headers = {}
    this.isAuthenticated = false
    this.lastAuthTime = null
    this.debug = true
  }

  /**
   * 确保认证状态
   * 实现认证状态复用，避免重复认证
   */
  async ensureAuthenticated() {
    // 如果已认证且未过期，直接返回
    if (this.isAuthenticated && this.lastAuthTime && Date.now() - this.lastAuthTime < 30 * 60 * 1000) {
      // 30分钟有效期
      return
    }

    // 如果有调试 token，使用调试 token
    if (this.debug) {
      const debugToken =
        '73AE2E6CC13DD9673F421A1F3E02AED0E1BFB595FD663AFA63ED00682C85E0350ECBA76C0D9169C1842C895EC3C7FD43FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972EB9036F52182EC2D6CFEC251B6B3AD83385AA04082B6E13207380EE6E17F65D7D02746F0B1CB9D088DFB1EDE0D3D45D112B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E'
      // '3B033D35C0941BAC9E252C1ED3D433887A1B7CB77315C4F985E4AFA80FF96942FDF51AC7DC8A2C46C4D9874D3F6E8E6491FB0CBB6F4DD8CC94BE739FD776C7F4804D8EB8613305E973532E9BF4ABD72377FC271889007EB032442C06676583671FD2E6CA573F1E09F98BEBD48C504244151002FFD8A51141F93581D47431A97BFDCC8C33D029F98FABD1A061B8FFFA7E30C018A70CFD503A6AAC03FF0B09EE61C0ABBFBE4C30F683D7CF82F24CA79A3B4C4FFBD3D1FB387E'
      await this.initWithToken(debugToken)
    }
  }

  /**
   * 用户登录认证
   * @param {object} options - 登录参数
   * @returns {object} 登录结果
   */
  async login(options = {}) {
    const { username, password, isPhone = true } = options

    // 参数校验
    const validation = validateParams({ username, password }, ['username', 'password'])
    if (validation) {
      console.warn('[AuthManager] [login] 参数校验失败：', validation)
      return validation
    }

    try {
      const loginUrl = API_CONFIG.LOGIN_URL

      // 根据登录类型选择不同的字段名
      const loginData = isPhone
        ? {
            password: password,
            phone: username,
          }
        : {
            password: password,
            email: username,
          }

      const headers = API_CONFIG.LOGIN_HEADERS

      const result = await this._request('POST', loginUrl, loginData, headers)

      if (result.errCode) {
        console.error('[AuthManager] [login] 登录失败：', result)
        return result
      }

      // 从 cookies 中提取 token
      const token = extractTokenFromCookies(result.headers)
      if (!token) {
        console.error('[AuthManager] [login] 未能从响应中提取到 token')
        return createErrorResponse(ERROR_CODES.TOKEN_NOT_FOUND, '登录成功但未能获取到访问令牌')
      }

      // 保存认证信息
      this.token = token
      this.isAuthenticated = true
      this.lastAuthTime = Date.now()
      this.headers = {
        ...API_CONFIG.DEFAULT_HEADERS,
        Cookie: `t=${token}`,
      }

      return createSuccessResponse('登录成功', {
        token: token,
        loginType: isPhone ? '手机号' : '邮箱',
      })
    } catch (error) {
      console.error('[AuthManager] [login] 登录异常：', error)
      return createErrorResponse(ERROR_CODES.LOGIN_ERROR, error.message || '登录失败', error)
    }
  }

  /**
   * 使用已有 token 初始化
   * @param {string} token - 访问令牌
   * @returns {object} 初始化结果
   */
  async initWithToken(token) {
    if (!token) {
      return createErrorResponse(ERROR_CODES.PARAM_IS_NULL, 'token 不能为空')
    }

    try {
      // 保存认证信息
      this.token = token
      this.headers = {
        ...API_CONFIG.DEFAULT_HEADERS,
        Cookie:
          // 't=3B033D35C0941BAC9E252C1ED3D43388D132888417075DB0E9F6BA44BD93845803002E6CBBEDBD68BA7255A80453EC6CCB5915C648A555FA94BE739FD776C7F4804D8EB8613305E973532E9BF4ABD72377FC271889007EB032442C06676583678E25858B3D740ADD2614B26C46A73750151002FFD8A51141F93581D47431A97B1A38F2DE636CD9BFABD1A061B8FFFA7E30C018A70CFD503A6AAC03FF0B09EE61C0ABBFBE4C30F683D7CF82F24CA79A3B4C4FFBD3D1FB387E; AWSALB=4c0+EYjYxvXQsHmQtP5NMD/7duzMjvBHWD/JsdrBI4gujWL3k9ScxMoND7XZg7fjeQIo97tWG9TbiZzf6ngoRWwTDCAIlGWeZdUKFbxh/TYFGb/vYZO7f9d0g5ar; AWSALBCORS=4c0+EYjYxvXQsHmQtP5NMD/7duzMjvBHWD/JsdrBI4gujWL3k9ScxMoND7XZg7fjeQIo97tWG9TbiZzf6ngoRWwTDCAIlGWeZdUKFbxh/TYFGb/vYZO7f9d0g5ar',
		  '_clck=3px5ok%7C2%7Cfxu%7C0%7C1899; oai=4D484D1289F6F3C8D8E7D7A400131FCCEE2E602E5BC9000E051CBDCAEDFDB62CE1BAB308B2C4E99D43624FF97CF637D18D37EE8027C9A996E8D55A9CCFA8A5DE5059172DE0E8876F521945ECCE9A189815038B59E3A50B5B6A807729A64ED8BEACF32C4444BC7266F04824B683E385953362938A5B2EE4CECE8DB4389F85E4B0B47ABA1D433E2DA3B2CFD338FC34CB25F21AECD75E4C7C3775FADD224BBA7843; t=73AE2E6CC13DD9677EFD0A89CF6C763B2986FF30103D6FE545145B96CCB6B21CFA6E57396C675621B67E1D69A5B9E080FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972E1EB1CFB863A2ABD3F50F8F11079108A385AA04082B6E13207380EE6E17F65D766E21B5418294687E9E12277BCA35A9F12B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E; _csrf_token=WXAWkUifd84X9i2QALhHzWFGIhZFMMVav0mkAb5qwQ-1755571538; AWSALB=cGGvPsMf/sOIsFKHyxJuUO0Ga/I4Qjxi5R8m4XX0LGqUniL+F89qcrfr3yLMjucK9ADer5+6YYvAbr2aTyOMUMe1tDjOGo+UZHHs/fe+/BXgPycTm1H514Q2OG7+OlcmuAHPU0A5ZrQqKc1tYRrVHykgtD3Ejf9MtZcqL1cFDw0kaCPJdYrPXCVdyFkpAw==; AWSALBCORS=cGGvPsMf/sOIsFKHyxJuUO0Ga/I4Qjxi5R8m4XX0LGqUniL+F89qcrfr3yLMjucK9ADer5+6YYvAbr2aTyOMUMe1tDjOGo+UZHHs/fe+/BXgPycTm1H514Q2OG7+OlcmuAHPU0A5ZrQqKc1tYRrVHykgtD3Ejf9MtZcqL1cFDw0kaCPJdYrPXCVdyFkpAw=='
      }

      // 验证 token 有效性（通过获取基础数据）
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[AuthManager] [initWithToken] token 验证失败：', batchResult)
        this.token = null
        this.headers = {}
        this.isAuthenticated = false
        return createErrorResponse(ERROR_CODES.UNAUTHORIZED, 'token 无效或已过期')
      }

      this.isAuthenticated = true
      this.lastAuthTime = Date.now()

      return createSuccessResponse('token 初始化成功', { token: token })
    } catch (error) {
      console.error('[AuthManager] [initWithToken] 初始化异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || 'token 初始化失败', error)
    }
  }

  /**
   * 获取所有基础数据（任务、清单、标签）
   * @returns {object} 基础数据
   */
  async getBatchData() {
    try {
      const result = await this._request('GET', API_CONFIG.BATCH_DATA_URL)
      if (result.errCode) {
        console.error('[AuthManager] [getBatchData] 获取基础数据失败：', result)
        return result
      }

      const successResult = createSuccessResponse('获取基础数据成功', {
        tasks: result.data.syncTaskBean?.update || [],
        projects: result.data.projectProfiles || [],
        tags: result.data.tags || [],
      })

      return successResult
    } catch (error) {
      console.error('[AuthManager] [getBatchData] 获取基础数据异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取基础数据失败', error)
    }
  }

  /**
   * 统一的 HTTP 请求方法
   * @param {string} method - HTTP 方法
   * @param {string} url - 请求 URL
   * @param {object} data - 请求数据
   * @param {object} customHeaders - 自定义请求头
   * @returns {object} 响应结果
   */
  async _request(method, url, data = null, customHeaders = {}) {
    const requestStartTime = Date.now()
    const fullUrl = url.startsWith('http') ? url : `${API_CONFIG.BASE_URL}${url}`
    const headers = { ...this.headers, ...customHeaders }

    // 🔍 详细调试日志：请求信息
    console.log(`[AuthManager] [_request] === HTTP 请求开始 ===`)
    console.log(`[AuthManager] [_request] 接口名称: ${method} ${url}`)
    console.log(`[AuthManager] [_request] 完整URL: ${fullUrl}`)
    console.log(`[AuthManager] [_request] 请求方法: ${method}`)

    // 🔍 详细调试日志：请求头信息
    console.log(`[AuthManager] [_request] 请求头详情:`, {
      'Content-Type': headers['Content-Type'],
      Accept: headers['Accept'],
      'User-Agent': headers['User-Agent'],
      'X-Device': headers['X-Device'],
      Host: headers['Host'],
      Cookie: headers['Cookie'] ? `Cookie设置: ${headers['Cookie'].substring(0, 50)}...` : '未设置Cookie',
      其他头: Object.keys(headers).filter(
        (k) => !['Content-Type', 'Accept', 'User-Agent', 'X-Device', 'Host', 'Cookie'].includes(k)
      ),
    })

    // 🔍 详细调试日志：Cookie 解析
    if (headers['Cookie']) {
      const cookieMatch = headers['Cookie'].match(/t=([^;]+)/)
      if (cookieMatch) {
        console.log(
          `[AuthManager] [_request] Token 解析：t=${cookieMatch[1].substring(0, 20)}...${cookieMatch[1].substring(
            cookieMatch[1].length - 10
          )}`
        )
      } else {
        console.log(`[AuthManager] [_request] Cookie 格式异常：${headers['Cookie']}`)
      }
    } else {
      console.log(`[AuthManager] [_request] ⚠️ 未设置 Cookie，可能导致认证失败`)
    }

    // 🔍 详细调试日志：请求体
    if (data) {
      console.log(`[AuthManager] [_request] 请求体:`, JSON.stringify(data, null, 2))
    } else {
      console.log(`[AuthManager] [_request] 请求体：无`)
    }

    const requestOptions = {
      method: method,
      headers: headers,
      timeout: API_CONFIG.TIMEOUT,
    }

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestOptions.data = data
    }

    try {
      console.log(`[AuthManager] [_request] 发起请求...`)
      const response = await uniCloud.httpclient.request(fullUrl, requestOptions)
      const requestEndTime = Date.now()

      // 🔍 详细调试日志：响应信息
      console.log(`[AuthManager] [_request] === HTTP 响应接收 ===`)
      console.log(`[AuthManager] [_request] 响应状态：${response.status}`)
      console.log(`[AuthManager] [_request] 响应耗时：${requestEndTime - requestStartTime}ms`)
      console.log(`[AuthManager] [_request] 响应头:`, {
        'content-type': response.headers?.['content-type'],
        'set-cookie': response.headers?.['set-cookie'] ? '包含set-cookie' : '无 set-cookie',
        'content-length': response.headers?.['content-length'],
      })

      // 🔍 详细调试日志：响应体预览
      if (response.data) {
        try {
          let dataPreview = response.data
          if (dataPreview instanceof Buffer) {
            dataPreview = dataPreview.toString('utf-8')
          }
          if (typeof dataPreview === 'string') {
            dataPreview = JSON.parse(dataPreview)
          }
          console.log(`[AuthManager] [_request] 响应体预览:`, {
            hasData: !!dataPreview,
            dataKeys: typeof dataPreview === 'object' ? Object.keys(dataPreview) : 'non-object',
            syncTaskBeanCount: dataPreview?.syncTaskBean?.update?.length || 0,
            projectProfilesCount: dataPreview?.projectProfiles?.length || 0,
            tagsCount: dataPreview?.tags?.length || 0,
          })
        } catch (e) {
          console.log(`[AuthManager] [_request] 响应体解析失败:`, e.message)
        }
      }

      const parsedResponse = parseHttpResponse(response)

      console.log(`[AuthManager] [_request] === 请求处理完成 ===`)
      return parsedResponse
    } catch (error) {
      const requestEndTime = Date.now()

      console.error(`[AuthManager] [_request] === HTTP 请求异常 ===`, error, {
        method,
        fullUrl,
        errorName: error.name,
        errorMessage: error.message,
        errorCode: error.code,
        errorStack: error.stack,
        duration: requestEndTime - requestStartTime,
        endTime: new Date(requestEndTime).toISOString(),
        requestData: data ? JSON.stringify(data, null, 2) : 'no data',
      })

      // 特别关注任务创建相关的错误
      if (url.includes('/task') || (data && data.title)) {
        console.error(`[AuthManager] [_request] !!! 任务相关 HTTP 请求失败 !!!`, error, {
          method,
          url,
          fullUrl,
          requestData: data ? JSON.stringify(data, null, 2) : 'no data',
          errorDetails: {
            name: error.name,
            message: error.message,
            code: error.code,
            stack: error.stack,
          },
          duration: requestEndTime - requestStartTime,
          timestamp: new Date().toISOString(),
        })
      }

      if (error.code === 'TIMEOUT') {
        console.error(`[AuthManager] [_request] 请求超时`, {
          method,
          fullUrl,
          timeout: API_CONFIG.TIMEOUT,
          duration: requestEndTime - requestStartTime,
        })
        return createErrorResponse(ERROR_CODES.TIMEOUT_ERROR, '请求超时，请稍后重试')
      }

      return createErrorResponse(ERROR_CODES.NETWORK_ERROR, error.message || 'HTTP 请求失败', error)
    }
  }

  /**
   * 获取当前认证状态
   * @returns {object} 认证状态信息
   */
  getAuthStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      hasToken: !!this.token,
      lastAuthTime: this.lastAuthTime,
    }
  }

  /**
   * 清除认证状态
   */
  clearAuth() {
    this.token = null
    this.headers = {}
    this.isAuthenticated = false
    this.lastAuthTime = null
  }
}

module.exports = AuthManager
