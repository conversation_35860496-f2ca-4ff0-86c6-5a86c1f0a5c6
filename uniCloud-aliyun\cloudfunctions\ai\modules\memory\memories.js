// uniCloud-aliyun/cloudfunctions/ai/modules/memory/memories.js
const { MEMORY_CONFIG, MEMORY_ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  generateId,
  formatDateTime
} = require('./utils')

/**
 * 记忆管理类
 */
class MemoryManager {
  constructor() {
    this.db = uniCloud.database()
    this.collection = this.db.collection('memory')
  }

  /**
   * 创建记忆
   */
  async createMemory(options = {}) {
    const { userId, content, startDate = null, endDate = null } = options

    // 参数校验
    const validation = validateParams({ userId, content }, ['userId', 'content'])
    if (validation) return validation

    // 内容长度校验
    if (content.length > MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH) {
      return createErrorResponse(
        MEMORY_ERROR_CODES.CONTENT_TOO_LONG,
        `记忆内容不能超过${MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH}个字符`
      )
    }

    try {
      // 检查用户记忆数量限制
      const { total } = await this.collection.where({ userId }).count()
      if (total >= MEMORY_CONFIG.MAX_MEMORIES_PER_USER) {
        return createErrorResponse(
          MEMORY_ERROR_CODES.MEMORY_LIMIT_EXCEEDED,
          `每个用户最多只能保存${MEMORY_CONFIG.MAX_MEMORIES_PER_USER}条记忆`
        )
      }

      const now = formatDateTime()
      const memoryData = {
        _id: generateId(),
        userId,
        content: content.trim(),
        startDate,
        endDate,
        createTime: now,
        updateTime: now
      }

      const result = await this.collection.add(memoryData)

      return createSuccessResponse({
        memoryId: result.id,
        ...memoryData
      }, '记忆创建成功')

    } catch (error) {
      console.error('[MemoryManager.createMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 获取记忆列表
   */
  async getMemories(options = {}) {
    const { userId, limit = MEMORY_CONFIG.MEMORY_QUERY_LIMIT, offset = 0 } = options

    // 参数校验
    const validation = validateParams({ userId }, ['userId'])
    if (validation) return validation

    try {
      const query = this.collection
        .where({ userId })
        .orderBy('createTime', 'desc')
        .skip(offset)
        .limit(Math.min(limit, MEMORY_CONFIG.MAX_QUERY_LIMIT))

      const result = await query.get()

      return createSuccessResponse(result.data, '获取记忆列表成功')

    } catch (error) {
      console.error('[MemoryManager.getMemories] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 更新记忆
   */
  async updateMemory(options = {}) {
    const { userId, memoryId, content, startDate, endDate } = options

    // 参数校验
    const validation = validateParams({ userId, memoryId }, ['userId', 'memoryId'])
    if (validation) return validation

    try {
      const updateData = {
        updateTime: formatDateTime()
      }

      if (content !== undefined) {
        if (content.length > MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH) {
          return createErrorResponse(
            MEMORY_ERROR_CODES.CONTENT_TOO_LONG,
            `记忆内容不能超过${MEMORY_CONFIG.MEMORY_CONTENT_MAX_LENGTH}个字符`
          )
        }
        updateData.content = content.trim()
      }

      if (startDate !== undefined) updateData.startDate = startDate
      if (endDate !== undefined) updateData.endDate = endDate

      const result = await this.collection
        .where({ _id: memoryId, userId })
        .update(updateData)

      if (result.updated === 0) {
        return createErrorResponse(MEMORY_ERROR_CODES.MEMORY_NOT_FOUND, '记忆不存在或无权限访问')
      }

      return createSuccessResponse({ memoryId, ...updateData }, '记忆更新成功')

    } catch (error) {
      console.error('[MemoryManager.updateMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }

  /**
   * 删除记忆
   */
  async deleteMemory(options = {}) {
    const { userId, memoryId } = options

    // 参数校验
    const validation = validateParams({ userId, memoryId }, ['userId', 'memoryId'])
    if (validation) return validation

    try {
      const result = await this.collection
        .where({ _id: memoryId, userId })
        .remove()

      if (result.deleted === 0) {
        return createErrorResponse(MEMORY_ERROR_CODES.MEMORY_NOT_FOUND, '记忆不存在或无权限访问')
      }

      return createSuccessResponse({ memoryId }, '记忆删除成功')

    } catch (error) {
      console.error('[MemoryManager.deleteMemory] 数据库错误:', error)
      return createErrorResponse(MEMORY_ERROR_CODES.DATABASE_ERROR, error.message)
    }
  }
}

module.exports = MemoryManager
